# 自动打字教程脚本

这个脚本可以自动完成在线打字教程，自动检测需要输入的字符并模拟键盘输入。

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install selenium webdriver-manager
```

## 使用方法

### 方法1: 使用完整版本
```bash
python auto_typing.py
```

### 方法2: 使用简化版本
```bash
python simple_auto_typing.py
```

### 方法3: 使用edclub专用版本 (推荐)
```bash
python edclub_auto_typer.py
```

## 功能特点

- 自动检测页面上需要输入的字符
- 支持多种文本模式识别（中英文）
- 自动模拟键盘输入
- 自动处理课程进度
- 支持无头模式运行

## 脚本说明

### auto_typing.py
- 完整功能版本
- 支持更多的页面元素检测
- 包含详细的错误处理
- 支持自定义配置

### simple_auto_typing.py  
- 简化版本
- 代码更简洁
- 适合快速使用
- 自动管理ChromeDriver

## 注意事项

1. 确保已安装Chrome浏览器
2. 脚本会自动下载和管理ChromeDriver
3. 运行时请保持网络连接
4. 按Ctrl+C可以随时停止程序

## 自定义配置

可以修改脚本中的以下参数：
- `headless`: 是否无头模式运行
- `max_iterations`: 最大尝试次数
- `url`: 目标网站URL

## 故障排除

如果遇到问题：
1. 检查Chrome浏览器是否已安装
2. 确认网络连接正常
3. 尝试更新selenium版本
4. 检查目标网站是否可访问
