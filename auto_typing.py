#!/usr/bin/env python3
"""
自动打字教程脚本
使用Selenium自动化浏览器，检测并输入指定的字符
"""

import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class AutoTyping:
    def __init__(self, headless=False):
        """初始化浏览器"""
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
        
    def setup_driver(self, headless=False):
        """设置Chrome浏览器"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.wait = WebDriverWait(self.driver, 10)
        
    def open_typing_tutorial(self, url):
        """打开打字教程页面"""
        print(f"正在打开页面: {url}")
        self.driver.get(url)
        time.sleep(3)  # 等待页面加载
        
    def find_target_key(self):
        """检测需要输入的字符"""
        try:
            # 尝试多种可能的选择器来找到目标字符
            selectors = [
                # 查找包含"Type the"文本的元素
                "//*[contains(text(), 'Type the')]",
                # 查找高亮的键
                ".highlighted-key",
                ".target-key", 
                ".current-key",
                # 查找蓝色背景的键（从图片看是蓝色）
                "//*[contains(@class, 'key') and contains(@style, 'background')]",
                # 查找指令文本
                ".instruction",
                ".lesson-text",
                "h1", "h2", "h3", "p"
            ]
            
            for selector in selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        text = element.text.strip()
                        print(f"找到文本: {text}")
                        
                        # 使用正则表达式提取需要输入的字符
                        patterns = [
                            r"Type the '([^']+)' key",
                            r"Type the ([a-zA-Z0-9])\s+key",  # 匹配 "Type the a key"
                            r"Press '([^']+)'",
                            r"Press the ([a-zA-Z0-9])\s+key",
                            r"输入 '([^']+)'",
                            r"按 '([^']+)' 键",
                        ]
                        
                        for pattern in patterns:
                            match = re.search(pattern, text, re.IGNORECASE)
                            if match:
                                target_char = match.group(1)
                                print(f"检测到目标字符: {target_char}")
                                return target_char
                                
                except Exception as e:
                    continue
                    
            # 如果没有找到明确的指令，尝试查找页面标题或主要内容
            try:
                page_text = self.driver.find_element(By.TAG_NAME, "body").text
                print(f"页面内容: {page_text[:200]}...")

                # 从页面内容中提取字符 - 使用更灵活的模式
                patterns = [
                    r"Type the '([^']+)' key",
                    r"Type the ([a-zA-Z0-9])\s+key",  # 匹配 "Type the a key"
                    r"Press the ([a-zA-Z0-9])\s+key",
                ]

                for pattern in patterns:
                    match = re.search(pattern, page_text, re.IGNORECASE)
                    if match:
                        target_char = match.group(1)
                        print(f"从页面内容中找到目标字符: {target_char}")
                        return target_char

            except Exception as e:
                print(f"获取页面内容失败: {e}")
                
        except Exception as e:
            print(f"查找目标字符时出错: {e}")
            
        return None
        
    def simulate_keypress(self, key_char):
        """模拟按键输入"""
        try:
            print(f"正在输入字符: {key_char}")

            # 基于页面结构，优先尝试隐藏的输入框
            success = False

            # 方法1: 尝试找到页面上的隐藏输入框
            try:
                hidden_input = self.driver.find_element(By.CSS_SELECTOR, "input[type='text'][aria-hidden='true']")
                if hidden_input:
                    hidden_input.click()
                    hidden_input.clear()
                    hidden_input.send_keys(key_char)
                    success = True
                    print("使用隐藏输入框成功")
            except Exception as e:
                print(f"隐藏输入框方法失败: {e}")

            # 方法2: 尝试普通输入框
            if not success:
                try:
                    input_element = self.driver.find_element(By.CSS_SELECTOR, "input[type='text']")
                    input_element.click()
                    time.sleep(0.1)
                    input_element.send_keys(key_char)
                    success = True
                    print("使用普通输入框成功")
                except Exception as e:
                    print(f"普通输入框方法失败: {e}")

            # 方法3: 直接发送按键到页面
            if not success:
                try:
                    from selenium.webdriver.common.action_chains import ActionChains
                    # 先点击页面确保焦点
                    self.driver.find_element(By.TAG_NAME, "body").click()
                    time.sleep(0.2)
                    ActionChains(self.driver).send_keys(key_char).perform()
                    success = True
                    print("使用ActionChains成功")
                except Exception as e:
                    print(f"ActionChains方法失败: {e}")

            if success:
                print(f"✅ 成功输入字符: {key_char}")
                time.sleep(1)  # 等待响应
            else:
                print(f"❌ 所有输入方法都失败了")

        except Exception as e:
            print(f"输入字符时出错: {e}")
            
    def check_progress(self):
        """检查是否需要继续或进入下一课"""
        try:
            # 查找"下一步"、"继续"等按钮
            next_buttons = [
                "//button[contains(text(), 'Next')]",
                "//button[contains(text(), 'Continue')]", 
                "//button[contains(text(), '下一步')]",
                "//button[contains(text(), '继续')]",
                ".next-button",
                ".continue-button"
            ]
            
            for selector in next_buttons:
                try:
                    if selector.startswith("//"):
                        button = self.driver.find_element(By.XPATH, selector)
                    else:
                        button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if button.is_enabled():
                        print("找到下一步按钮，点击继续")
                        button.click()
                        time.sleep(2)
                        return True
                except:
                    continue
                    
        except Exception as e:
            print(f"检查进度时出错: {e}")
            
        return False
        
    def run_auto_typing(self, url, max_iterations=50):
        """运行自动打字"""
        try:
            self.open_typing_tutorial(url)
            
            for i in range(max_iterations):
                print(f"\n=== 第 {i+1} 次尝试 ===")
                
                # 检测目标字符
                target_char = self.find_target_key()
                
                if target_char:
                    # 输入字符
                    self.simulate_keypress(target_char)
                    
                    # 等待一下让页面响应
                    time.sleep(2)
                    
                    # 检查是否需要进入下一步
                    if not self.check_progress():
                        # 如果没有下一步按钮，等待页面自动跳转
                        time.sleep(3)
                else:
                    print("未能检测到目标字符，尝试刷新页面")
                    self.driver.refresh()
                    time.sleep(3)
                    
                # 检查是否完成了所有课程
                if "完成" in self.driver.page_source or "Complete" in self.driver.page_source:
                    print("课程已完成！")
                    break
                    
        except KeyboardInterrupt:
            print("\n用户中断了程序")
        except Exception as e:
            print(f"运行过程中出错: {e}")
        finally:
            self.cleanup()
            
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
            print("浏览器已关闭")

def main():
    """主函数"""
    url = "https://www.edclub.com/sportal/program-3/127.play"
    
    print("自动打字教程脚本启动")
    print("按 Ctrl+C 可以随时停止程序")
    
    auto_typer = AutoTyping(headless=False)  # 设置为True可以无头模式运行
    auto_typer.run_auto_typing(url)

if __name__ == "__main__":
    main()
