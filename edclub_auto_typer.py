#!/usr/bin/env python3
"""
专门针对edclub.com/TypingClub优化的自动打字脚本
基于页面结构分析优化
"""

import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class EdclubAutoTyper:
    def __init__(self, headless=False):
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
        
    def setup_driver(self, headless=False):
        """设置Chrome浏览器"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.wait = WebDriverWait(self.driver, 10)
        
    def open_lesson(self, url):
        """打开课程页面"""
        print(f"🚀 打开课程: {url}")
        self.driver.get(url)
        time.sleep(5)  # 等待页面完全加载
        
    def get_lesson_text(self):
        """获取课程文本内容"""
        try:
            # 等待页面加载完成
            time.sleep(2)
            
            # 获取页面文本
            body_text = self.driver.find_element(By.TAG_NAME, "body").text
            
            # 清理文本，移除CSS和无关内容
            lines = body_text.split('\n')
            clean_lines = []
            
            for line in lines:
                line = line.strip()
                # 过滤掉CSS样式、空行和无关内容
                if (line and 
                    not line.startswith('.') and 
                    not line.startswith('{') and 
                    not line.startswith('}') and
                    'stroke' not in line.lower() and
                    'fill' not in line.lower() and
                    'font-family' not in line.lower() and
                    'opacity' not in line.lower() and
                    len(line) < 200):  # 过滤掉很长的CSS行
                    clean_lines.append(line)
            
            clean_text = '\n'.join(clean_lines)
            print(f"📝 课程内容:\n{clean_text[:300]}...")
            return clean_text
            
        except Exception as e:
            print(f"❌ 获取课程文本失败: {e}")
            return ""
    
    def extract_target_characters(self, text):
        """从文本中提取需要输入的字符"""
        print(f"🔍 分析文本内容...")
        
        # 多种模式匹配
        patterns = [
            r"Type the ([a-zA-Z0-9;:,.<>/?'\"\\|`~!@#$%^&*()_+\-=\[\]{}])\s+key",  # 包含特殊字符
            r"Type the '([^']+)' key",                 # "Type the 'a' key"
            r"Press the ([a-zA-Z0-9;:,.<>/?'\"\\|`~!@#$%^&*()_+\-=\[\]{}])\s+key", # 包含特殊字符
            r"按 ([a-zA-Z0-9;:,.<>/?'\"\\|`~!@#$%^&*()_+\-=\[\]{}])\s+键",          # 中文版本
            r"NEW KEY INTRODUCTION\s*Type the\s*([a-zA-Z0-9;:,.<>/?'\"\\|`~!@#$%^&*()_+\-=\[\]{}])", # 特殊格式
        ]
        
        for i, pattern in enumerate(patterns):
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            if matches:
                char = matches[0]
                print(f"✅ 模式 {i+1} 匹配成功: '{char}'")
                return char
        
        # 如果没有匹配到模式，尝试查找单独的字符行
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if (line and len(line) == 1 and 
                line.isalnum() and 
                line not in ['a', 'A']):  # 避免误匹配常见单词
                print(f"🎯 找到单字符行: '{line}'")
                return line
        
        print("❌ 未找到目标字符")
        return None
    
    def input_character(self, char):
        """输入字符到页面"""
        print(f"⌨️  准备输入字符: '{char}'")
        
        success = False

        # 方法1: 直接发送按键到页面（最有效的方法）
        try:
            # 确保页面有焦点
            self.driver.find_element(By.TAG_NAME, "body").click()
            time.sleep(0.3)
            ActionChains(self.driver).send_keys(char).perform()
            success = True
            print("✅ 方法1成功: ActionChains")
        except Exception as e:
            print(f"⚠️  方法1失败: {e}")

        # 方法2: 使用隐藏的输入框（备用）
        if not success:
            try:
                hidden_input = self.driver.find_element(
                    By.CSS_SELECTOR,
                    "input[type='text'][aria-hidden='true']"
                )
                if hidden_input:
                    hidden_input.click()
                    hidden_input.clear()
                    hidden_input.send_keys(char)
                    success = True
                    print("✅ 方法2成功: 隐藏输入框")
            except Exception as e:
                print(f"⚠️  方法2失败: {e}")

        # 方法3: 查找任何可见的输入框
        if not success:
            try:
                input_elements = self.driver.find_elements(By.CSS_SELECTOR, "input[type='text']")
                if input_elements:
                    input_element = input_elements[0]
                    input_element.click()
                    time.sleep(0.2)
                    input_element.clear()
                    input_element.send_keys(char)
                    success = True
                    print("✅ 方法3成功: 普通输入框")
            except Exception as e:
                print(f"⚠️  方法3失败: {e}")
        
        # 方法4: 尝试发送到特定的容器
        if not success:
            try:
                # 查找可能的输入容器
                containers = self.driver.find_elements(By.CSS_SELECTOR, "#instructions, #root, .keyboard-plugin")
                if containers:
                    container = containers[0]
                    container.click()
                    time.sleep(0.2)
                    ActionChains(self.driver).send_keys(char).perform()
                    success = True
                    print("✅ 方法4成功: 容器输入")
            except Exception as e:
                print(f"⚠️  方法4失败: {e}")
        
        if success:
            print(f"🎉 成功输入字符: '{char}'")
            time.sleep(1.5)  # 等待页面响应
            return True
        else:
            print(f"❌ 所有输入方法都失败了")
            return False
    
    def check_progress(self):
        """检查课程进度"""
        try:
            time.sleep(2)  # 等待页面更新
            
            # 检查是否有进度变化或新的指令
            current_text = self.get_lesson_text()
            
            # 检查是否完成当前课程
            completion_indicators = [
                'complete', 'completed', 'finished', 'done', 
                '完成', '结束', 'next lesson', 'continue'
            ]
            
            for indicator in completion_indicators:
                if indicator.lower() in current_text.lower():
                    print(f"🎯 检测到完成指示: {indicator}")
                    return 'completed'
            
            # 检查是否有新的字符需要输入
            new_char = self.extract_target_characters(current_text)
            if new_char:
                print(f"🔄 检测到新字符: {new_char}")
                return new_char
            
            return 'continue'
            
        except Exception as e:
            print(f"❌ 检查进度时出错: {e}")
            return 'error'
    
    def run_lesson(self, url, max_attempts=30):
        """运行自动打字课程"""
        try:
            self.open_lesson(url)
            
            for attempt in range(max_attempts):
                print(f"\n🔄 === 第 {attempt + 1} 次尝试 ===")
                
                # 获取当前课程内容
                lesson_text = self.get_lesson_text()
                if not lesson_text:
                    print("⚠️  无法获取课程内容，等待后重试...")
                    time.sleep(3)
                    continue
                
                # 提取目标字符
                target_char = self.extract_target_characters(lesson_text)
                if not target_char:
                    print("⚠️  未找到目标字符，检查进度...")
                    progress = self.check_progress()
                    if progress == 'completed':
                        print("🎉 课程已完成！")
                        break
                    time.sleep(2)
                    continue
                
                # 输入字符
                if self.input_character(target_char):
                    # 检查进度
                    progress = self.check_progress()
                    if progress == 'completed':
                        print("🎉 课程已完成！")
                        break
                    elif isinstance(progress, str) and len(progress) == 1:
                        # 如果返回了新字符，继续下一轮
                        continue
                else:
                    print("⚠️  输入失败，等待后重试...")
                    time.sleep(2)
                
                # 防止过快操作
                time.sleep(1)
            
            print(f"📊 完成 {attempt + 1} 次尝试")
            
        except KeyboardInterrupt:
            print("\n⏹️  用户中断了程序")
        except Exception as e:
            print(f"❌ 运行过程中出错: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
            print("🔚 浏览器已关闭")

def main():
    """主函数"""
    url = "https://www.edclub.com/sportal/program-3/127.play"
    
    print("🎯 edclub自动打字脚本启动")
    print("📝 专门针对TypingClub优化")
    print("⏹️  按 Ctrl+C 可以随时停止程序\n")
    
    auto_typer = EdclubAutoTyper(headless=False)
    auto_typer.run_lesson(url)

if __name__ == "__main__":
    main()
