#!/usr/bin/env python3
"""
EdClub 快速自动打字脚本 - 优化版本
专门针对 TypingClub 的字符序列练习优化
"""

import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.action_chains import ActionChains

class FastEdclubTyper:
    def __init__(self, headless=False):
        """初始化浏览器"""
        print("🚀 初始化快速打字脚本...")
        
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1200,800")
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.implicitly_wait(2)  # 减少等待时间
        
    def open_lesson(self, url):
        """打开课程页面"""
        print(f"🚀 打开课程: {url}")
        self.driver.get(url)
        time.sleep(3)  # 减少等待时间
        
    def find_current_character(self):
        """快速查找当前需要输入的字符"""
        try:
            # 方法1: 查找橙色背景的元素
            orange_elements = self.driver.find_elements(
                By.XPATH, 
                "//*[contains(@style, 'background') and contains(@style, 'rgb(255, 165, 0)') or contains(@style, 'orange')]"
            )
            
            for element in orange_elements:
                if element.is_displayed():
                    char = element.text.strip()
                    if char and len(char) == 1:
                        print(f"🎯 找到橙色高亮字符: '{char}'")
                        return char
            
            # 方法2: 查找包含active/current类的元素
            active_elements = self.driver.find_elements(
                By.XPATH,
                "//*[contains(@class, 'active') or contains(@class, 'current') or contains(@class, 'highlight')]"
            )
            
            for element in active_elements:
                if element.is_displayed():
                    char = element.text.strip()
                    if char and len(char) == 1:
                        print(f"🎯 找到活跃字符: '{char}'")
                        return char
            
            # 方法3: 查找页面上方的单字符元素
            all_chars = self.driver.find_elements(
                By.XPATH,
                "//*[string-length(normalize-space(text()))=1]"
            )
            
            for element in all_chars:
                if element.is_displayed():
                    location = element.location
                    if location['y'] < 400:  # 页面上半部分
                        char = element.text.strip()
                        if char and char.isalnum():
                            print(f"🎯 找到字符: '{char}' (位置: y={location['y']})")
                            return char
            
            return None
            
        except Exception as e:
            print(f"❌ 查找字符时出错: {e}")
            return None
    
    def input_character(self, char):
        """快速输入字符"""
        try:
            print(f"⌨️  输入字符: '{char}'")
            
            # 确保页面有焦点
            self.driver.find_element(By.TAG_NAME, "body").click()
            time.sleep(0.1)  # 很短的等待
            
            # 直接发送按键
            ActionChains(self.driver).send_keys(char).perform()
            time.sleep(0.2)  # 很短的等待
            
            print(f"✅ 成功输入: '{char}'")
            return True
            
        except Exception as e:
            print(f"❌ 输入失败: {e}")
            return False
    
    def check_continue_button(self):
        """检查并点击Continue按钮"""
        try:
            # 快速查找Continue按钮
            continue_selectors = [
                "//button[contains(text(), 'Continue')]",
                "//button[contains(text(), 'continue')]",
                "//*[contains(text(), 'Continue') and (self::button or self::a or self::div)]"
            ]
            
            for selector in continue_selectors:
                try:
                    button = self.driver.find_element(By.XPATH, selector)
                    if button.is_displayed() and button.is_enabled():
                        print("🎯 点击Continue按钮...")
                        button.click()
                        time.sleep(1)  # 短暂等待页面加载
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            print(f"❌ 检查Continue按钮时出错: {e}")
            return False
    
    def is_success_page(self):
        """快速检查是否在成功页面"""
        try:
            page_text = self.driver.page_source.lower()
            return any(indicator in page_text for indicator in [
                "good job", "continue", "next we will practice", "well done"
            ])
        except:
            return False
    
    def run_fast_lesson(self, url, max_attempts=100):
        """运行快速打字课程"""
        try:
            self.open_lesson(url)
            
            for attempt in range(max_attempts):
                print(f"\n🔄 第 {attempt + 1} 次")
                
                # 检查是否需要点击Continue
                if self.is_success_page():
                    if self.check_continue_button():
                        continue
                
                # 查找当前字符
                char = self.find_current_character()
                if not char:
                    print("⚠️  未找到字符，等待...")
                    time.sleep(0.5)
                    continue
                
                # 输入字符
                if self.input_character(char):
                    # 短暂等待页面响应
                    time.sleep(0.3)
                    
                    # 检查是否出现Continue按钮
                    if self.is_success_page():
                        self.check_continue_button()
                else:
                    print("⚠️  输入失败，重试...")
                    time.sleep(0.5)
                
                # 很短的循环间隔
                time.sleep(0.1)
            
            print(f"📊 完成 {attempt + 1} 次尝试")
            
        except KeyboardInterrupt:
            print("\n⏹️  用户中断了程序")
        except Exception as e:
            print(f"❌ 运行过程中出错: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
            print("🔚 浏览器已关闭")

def main():
    """主函数"""
    url = "https://www.edclub.com/sportal/program-3/127.play"
    
    print("🎯 EdClub 快速自动打字脚本")
    print("⚡ 优化版本 - 更快更准确")
    print("⏹️  按 Ctrl+C 可以随时停止程序\n")
    
    typer = FastEdclubTyper(headless=False)
    typer.run_fast_lesson(url)

if __name__ == "__main__":
    main()
