#!/usr/bin/env python3
"""
快速启动脚本
"""

import subprocess
import sys
import os

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖安装完成！")
        return True
    except subprocess.CalledProcessError:
        print("依赖安装失败，请手动安装：")
        print("pip install selenium webdriver-manager")
        return False

def main():
    """主函数"""
    print("=== 自动打字教程脚本 ===")
    print("1. 完整版本 (auto_typing.py)")
    print("2. 简化版本 (simple_auto_typing.py)")
    print("3. 安装依赖")
    print("4. 退出")
    
    while True:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            if os.path.exists("auto_typing.py"):
                print("启动完整版本...")
                subprocess.run([sys.executable, "auto_typing.py"])
            else:
                print("文件不存在: auto_typing.py")
            break
            
        elif choice == "2":
            if os.path.exists("simple_auto_typing.py"):
                print("启动简化版本...")
                subprocess.run([sys.executable, "simple_auto_typing.py"])
            else:
                print("文件不存在: simple_auto_typing.py")
            break
            
        elif choice == "3":
            install_dependencies()
            
        elif choice == "4":
            print("退出程序")
            break
            
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
