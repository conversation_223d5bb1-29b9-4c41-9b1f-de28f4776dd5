#!/usr/bin/env python3
"""
简化版自动打字脚本
"""

import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def setup_driver():
    """设置浏览器驱动"""
    chrome_options = Options()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def find_and_type(driver):
    """查找并输入字符"""
    try:
        # 获取页面文本
        body_text = driver.find_element(By.TAG_NAME, "body").text
        print(f"页面内容: {body_text}")
        
        # 查找需要输入的字符
        patterns = [
            r"Type the '([^']+)' key",
            r"Press '([^']+)'",
            r"按 '([^']+)' 键",
        ]
        
        for pattern in patterns:
            match = re.search(pattern, body_text, re.IGNORECASE)
            if match:
                target_char = match.group(1)
                print(f"找到目标字符: {target_char}")
                
                # 尝试输入字符
                try:
                    # 方法1: 查找输入框
                    input_element = driver.find_element(By.CSS_SELECTOR, "input, textarea")
                    input_element.click()
                    input_element.send_keys(target_char)
                except:
                    # 方法2: 直接发送按键到页面
                    from selenium.webdriver.common.action_chains import ActionChains
                    ActionChains(driver).send_keys(target_char).perform()
                
                print(f"已输入字符: {target_char}")
                return True
                
    except Exception as e:
        print(f"处理时出错: {e}")
        
    return False

def main():
    """主函数"""
    url = "https://www.edclub.com/sportal/program-3/127.play"
    
    print("启动自动打字脚本...")
    driver = setup_driver()
    
    try:
        print(f"打开页面: {url}")
        driver.get(url)
        time.sleep(5)  # 等待页面加载
        
        # 循环处理
        for i in range(20):  # 最多尝试20次
            print(f"\n--- 第 {i+1} 次尝试 ---")
            
            if find_and_type(driver):
                time.sleep(3)  # 等待页面响应
                
                # 检查是否有下一步按钮
                try:
                    next_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Next') or contains(text(), '下一步')]")
                    if next_button.is_enabled():
                        next_button.click()
                        time.sleep(2)
                except:
                    pass
            else:
                print("未找到目标字符，等待...")
                time.sleep(2)
                
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序出错: {e}")
    finally:
        driver.quit()
        print("浏览器已关闭")

if __name__ == "__main__":
    main()
