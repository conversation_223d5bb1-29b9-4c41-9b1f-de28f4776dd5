#!/usr/bin/env python3
"""
测试版自动打字脚本 - 专门针对edclub网站优化
"""

import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.action_chains import ActionChains

def setup_driver():
    """设置浏览器驱动"""
    chrome_options = Options()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def extract_target_char(text):
    """从文本中提取目标字符"""
    print(f"分析文本: {repr(text)}")
    
    # 多种模式匹配
    patterns = [
        r"Type the ([a-zA-Z0-9])\s+key",  # "Type the a key"
        r"Type the '([^']+)' key",        # "Type the 'a' key"
        r"Press the ([a-zA-Z0-9])\s+key", # "Press the a key"
        r"按 ([a-zA-Z0-9])\s+键",          # 中文版本
    ]
    
    for i, pattern in enumerate(patterns):
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            char = match.group(1)
            print(f"模式 {i+1} 匹配成功: '{char}'")
            return char
    
    # 如果没有匹配，尝试查找单独的字符
    lines = text.split('\n')
    for line in lines:
        line = line.strip()
        if line and len(line) == 1 and line.isalnum():
            print(f"找到单字符: '{line}'")
            return line
    
    return None

def find_and_type(driver):
    """查找并输入字符"""
    try:
        # 获取页面文本
        body_text = driver.find_element(By.TAG_NAME, "body").text
        
        # 清理文本，移除CSS样式等干扰
        clean_lines = []
        for line in body_text.split('\n'):
            line = line.strip()
            # 跳过CSS样式行和空行
            if (line and 
                not line.startswith('.') and 
                not line.startswith('{') and 
                not line.startswith('}') and
                'stroke' not in line and
                'fill' not in line and
                'font-family' not in line):
                clean_lines.append(line)
        
        clean_text = '\n'.join(clean_lines)
        print(f"清理后的文本:\n{clean_text}")
        
        # 提取目标字符
        target_char = extract_target_char(clean_text)
        
        if target_char:
            print(f"准备输入字符: '{target_char}'")
            
            # 尝试多种输入方式
            success = False
            
            # 方式1: 查找输入框
            try:
                input_elements = driver.find_elements(By.CSS_SELECTOR, "input[type='text'], textarea, [contenteditable='true']")
                if input_elements:
                    input_element = input_elements[0]
                    input_element.click()
                    time.sleep(0.5)
                    input_element.clear()
                    input_element.send_keys(target_char)
                    success = True
                    print("方式1成功: 输入框输入")
            except Exception as e:
                print(f"方式1失败: {e}")
            
            # 方式2: 直接发送按键到页面
            if not success:
                try:
                    ActionChains(driver).send_keys(target_char).perform()
                    success = True
                    print("方式2成功: 直接按键")
                except Exception as e:
                    print(f"方式2失败: {e}")
            
            # 方式3: 点击页面后发送按键
            if not success:
                try:
                    driver.find_element(By.TAG_NAME, "body").click()
                    time.sleep(0.5)
                    ActionChains(driver).send_keys(target_char).perform()
                    success = True
                    print("方式3成功: 点击后按键")
                except Exception as e:
                    print(f"方式3失败: {e}")
            
            if success:
                print(f"✅ 成功输入字符: '{target_char}'")
                return True
            else:
                print("❌ 所有输入方式都失败了")
                
        else:
            print("❌ 未找到目标字符")
            
    except Exception as e:
        print(f"处理时出错: {e}")
        
    return False

def main():
    """主函数"""
    url = "https://www.edclub.com/sportal/program-3/127.play"
    
    print("🚀 启动测试版自动打字脚本...")
    driver = setup_driver()
    
    try:
        print(f"📖 打开页面: {url}")
        driver.get(url)
        time.sleep(5)  # 等待页面加载
        
        # 循环处理
        for i in range(10):  # 最多尝试10次
            print(f"\n🔄 === 第 {i+1} 次尝试 ===")
            
            if find_and_type(driver):
                print("⏳ 等待页面响应...")
                time.sleep(3)
                
                # 检查是否有下一步按钮或自动跳转
                try:
                    # 查找各种可能的下一步按钮
                    next_selectors = [
                        "button:contains('Next')",
                        "button:contains('Continue')", 
                        ".next-button",
                        ".continue-button",
                        "[onclick*='next']",
                        "[onclick*='continue']"
                    ]
                    
                    button_found = False
                    for selector in next_selectors:
                        try:
                            if ':contains(' in selector:
                                # 使用XPath处理contains
                                xpath = f"//button[contains(text(), '{selector.split(':contains(')[1].split(')')[0].strip(\"'\")}')]"
                                button = driver.find_element(By.XPATH, xpath)
                            else:
                                button = driver.find_element(By.CSS_SELECTOR, selector)
                            
                            if button.is_enabled():
                                button.click()
                                print("🔘 点击了下一步按钮")
                                button_found = True
                                break
                        except:
                            continue
                    
                    if not button_found:
                        print("🔄 没有找到下一步按钮，等待自动跳转...")
                        time.sleep(2)
                        
                except Exception as e:
                    print(f"检查下一步按钮时出错: {e}")
                    
            else:
                print("❌ 本次尝试失败，等待后重试...")
                time.sleep(2)
                
            # 检查是否完成
            current_text = driver.find_element(By.TAG_NAME, "body").text
            if any(word in current_text.lower() for word in ['complete', '完成', 'finished', 'done']):
                print("🎉 课程已完成！")
                break
                
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序出错: {e}")
    finally:
        driver.quit()
        print("🔚 浏览器已关闭")

if __name__ == "__main__":
    main()
